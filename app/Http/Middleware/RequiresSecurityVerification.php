<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\SecurityVerificationService;

class RequiresSecurityVerification
{
    protected $securityService;

    public function __construct(SecurityVerificationService $securityService)
    {
        $this->securityService = $securityService;
    }

    /**
     * Get secure pages from configuration
     */
    protected function getSecurePages(): array
    {
        return config('security.protected_routes', [
            '/account/phone/setup',
            '/account/email/setup',
            '/account/password/change',
            '/account/2fa/setup',
            '/account/create-username',
            '/account/address/manage',
            '/account/address/setup',
        ]);
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only apply to secure pages
        if (!$this->isSecurePage($request->path())) {
            return $next($request);
        }

        // Get cookie configuration
        $cookieConfig = $this->securityService->getCookieConfig();
        $cookieValue = $request->cookie($cookieConfig['name']);

        // Validate security cookie using the enhanced service
        if (!$cookieValue || !$this->isValidSecurityCookie($cookieValue)) {
            $user = auth()->user();
            $nextUrl = $request->fullUrl();

            \Log::info('Security verification required', [
                'path' => $request->path(),
                'full_url' => $nextUrl,
                'user_id' => $user->id ?? null,
                'cookie_exists' => !!$cookieValue,
                'has_completed_verification_before' => $user && $user->first_security_verification_at ? true : false
            ]);

            // Scenario 1: User has never completed security verification
            if (!$user || !$user->first_security_verification_at) {
                \Log::info('First-time security verification required - redirecting to security checkup', [
                    'user_id' => $user->id ?? null,
                    'intended_url' => $nextUrl
                ]);
                // Redirect to security check with next parameter for first-time users
                return redirect()->to('/security-check?next=' . urlencode($nextUrl));
            }

            // Scenario 2: User has completed verification before but cookie expired
            \Log::info('Security cookie expired for returning user - redirecting to previous page', [
                'user_id' => $user->id,
                'first_verification_at' => $user->first_security_verification_at,
                'intended_url' => $nextUrl,
                'referrer' => $request->header('Referer')
            ]);

            // For users with expired cookies, redirect back to a safe page
            $referrer = $request->header('Referer');
            $redirectUrl = '/account/overview'; // Default fallback

            // If there's a valid referrer from the same domain, use it
            if ($referrer && $this->isValidReferrer($referrer)) {
                try {
                    $parsedUrl = parse_url($referrer);
                    if (isset($parsedUrl['path'])) {
                        $redirectUrl = $parsedUrl['path'];
                        if (isset($parsedUrl['query'])) {
                            $redirectUrl .= '?' . $parsedUrl['query'];
                        }
                    }
                } catch (\Exception $e) {
                    \Log::warning('Failed to parse referrer URL', [
                        'referrer' => $referrer,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            return redirect()->to($redirectUrl)->with('security_expired', 'Your security session has expired. Please try again.');
        }

        \Log::debug('Security verification passed', [
            'path' => $request->path(),
            'user_id' => auth()->id()
        ]);

        return $next($request);
    }

    /**
     * Check if the current path is a secure page
     */
    protected function isSecurePage(string $path): bool
    {
        $securePages = $this->getSecurePages();

        foreach ($securePages as $securePage) {
            if (str_starts_with('/' . $path, $securePage)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Validate the security verification cookie using enhanced security service
     */
    protected function isValidSecurityCookie($cookieValue): bool
    {
        if (!$cookieValue) {
            return false;
        }

        // Use the security service for validation with cryptographic verification
        $payload = $this->securityService->validateSecureCookieValue($cookieValue);

        if (!$payload) {
            \Log::warning('Invalid security cookie detected', [
                'user_id' => auth()->id(),
                'cookie_preview' => substr($cookieValue, 0, 20) . '...',
                'ip_address' => request()->ip()
            ]);
            return false;
        }

        // Additional validation: check if user_id matches current user
        if (auth()->check() && isset($payload['user_id'])) {
            if ($payload['user_id'] != auth()->id()) {
                \Log::warning('Security cookie user mismatch', [
                    'cookie_user_id' => $payload['user_id'],
                    'current_user_id' => auth()->id(),
                    'ip_address' => request()->ip()
                ]);
                return false;
            }
        }

        return true;
    }

    /**
     * Check if the referrer URL is valid and safe for redirection
     */
    protected function isValidReferrer(string $referrer): bool
    {
        // Don't redirect back to security-check page
        if (str_contains($referrer, '/security-check')) {
            return false;
        }

        // Don't redirect to auth pages
        $authPages = ['/login', '/signup', '/forget-password', '/verify-email'];
        foreach ($authPages as $authPage) {
            if (str_contains($referrer, $authPage)) {
                return false;
            }
        }

        // For relative URLs, allow them
        if (!str_starts_with($referrer, 'http')) {
            return true;
        }

        // For absolute URLs, check if they're from the same domain
        try {
            $parsedUrl = parse_url($referrer);
            $currentHost = request()->getHost();

            if (isset($parsedUrl['host']) && $parsedUrl['host'] !== $currentHost) {
                return false;
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
