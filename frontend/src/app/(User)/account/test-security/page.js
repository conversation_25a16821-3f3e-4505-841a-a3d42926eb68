'use client';
import React, { useState, useEffect } from 'react';
import { useSecurityCookieMonitor } from "@/hooks/useSecurityCookieMonitor";
import AccountLayout from "@/Layouts/AccountLayout";

export default function TestSecurityPage() {
    const [cookieInfo, setCookieInfo] = useState(null);
    const [timeRemaining, setTimeRemaining] = useState(null);

    // Enable security cookie monitoring for this test page
    const { isMonitoring, checkCookie } = useSecurityCookieMonitor(true, {
        fallbackUrl: '/account/overview',
        expiryMinutes: 1 // 1 minute for testing
    });

    // Function to get cookie info
    const getCookieInfo = () => {
        const cookieValue = document.cookie
            .split('; ')
            .find(row => row.startsWith('security_verified='))
            ?.split('=')[1];

        if (cookieValue) {
            setCookieInfo({
                exists: true,
                value: cookieValue.substring(0, 8) + '...',
                length: cookieValue.length,
                isSessionToken: cookieValue.length === 64 && /^[a-f0-9]+$/i.test(cookieValue)
            });
        } else {
            setCookieInfo({
                exists: false,
                value: null,
                length: 0,
                isSessionToken: false
            });
        }
    };

    // Update cookie info every second
    useEffect(() => {
        getCookieInfo();
        const interval = setInterval(getCookieInfo, 1000);
        return () => clearInterval(interval);
    }, []);

    // Calculate time remaining (rough estimate)
    useEffect(() => {
        const startTime = Date.now();
        const expiryTime = startTime + (1 * 60 * 1000); // 1 minute

        const updateTimeRemaining = () => {
            const now = Date.now();
            const remaining = Math.max(0, expiryTime - now);
            setTimeRemaining(Math.ceil(remaining / 1000));
        };

        updateTimeRemaining();
        const interval = setInterval(updateTimeRemaining, 1000);
        return () => clearInterval(interval);
    }, []);

    const handleManualCheck = () => {
        checkCookie();
        getCookieInfo();
    };

    const handleDeleteCookie = () => {
        document.cookie = 'security_verified=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        getCookieInfo();
    };

    return (
        <AccountLayout>
            <div style={{ padding: '20px', maxWidth: '800px' }}>
                <h1>Security Cookie Monitor Test Page</h1>
                <p>This page demonstrates real-time security cookie monitoring. The cookie should expire after 1 minute and automatically redirect you back to the account overview page.</p>
                
                <div style={{ 
                    background: '#f8f9fa', 
                    padding: '20px', 
                    borderRadius: '8px', 
                    marginBottom: '20px',
                    border: '1px solid #dee2e6'
                }}>
                    <h3>Monitoring Status</h3>
                    <p><strong>Monitoring Active:</strong> {isMonitoring ? '✅ Yes' : '❌ No'}</p>
                    <p><strong>Estimated Time Remaining:</strong> {timeRemaining !== null ? `${timeRemaining} seconds` : 'Calculating...'}</p>
                </div>

                <div style={{ 
                    background: '#f8f9fa', 
                    padding: '20px', 
                    borderRadius: '8px', 
                    marginBottom: '20px',
                    border: '1px solid #dee2e6'
                }}>
                    <h3>Cookie Information</h3>
                    {cookieInfo ? (
                        <>
                            <p><strong>Cookie Exists:</strong> {cookieInfo.exists ? '✅ Yes' : '❌ No'}</p>
                            {cookieInfo.exists && (
                                <>
                                    <p><strong>Cookie Value:</strong> {cookieInfo.value}</p>
                                    <p><strong>Cookie Length:</strong> {cookieInfo.length} characters</p>
                                    <p><strong>Is Session Token:</strong> {cookieInfo.isSessionToken ? '✅ Yes' : '❌ No'}</p>
                                </>
                            )}
                        </>
                    ) : (
                        <p>Loading cookie information...</p>
                    )}
                </div>

                <div style={{ 
                    background: '#fff3cd', 
                    padding: '20px', 
                    borderRadius: '8px', 
                    marginBottom: '20px',
                    border: '1px solid #ffeaa7'
                }}>
                    <h3>Test Instructions</h3>
                    <ol>
                        <li>Wait for the cookie to expire (1 minute) - you should be automatically redirected</li>
                        <li>Or click "Delete Cookie" to simulate immediate expiration</li>
                        <li>Or click "Manual Check" to force a cookie validation check</li>
                    </ol>
                </div>

                <div style={{ marginBottom: '20px' }}>
                    <button 
                        onClick={handleManualCheck}
                        style={{
                            background: '#007bff',
                            color: 'white',
                            border: 'none',
                            padding: '10px 20px',
                            borderRadius: '4px',
                            marginRight: '10px',
                            cursor: 'pointer'
                        }}
                    >
                        Manual Check
                    </button>
                    
                    <button 
                        onClick={handleDeleteCookie}
                        style={{
                            background: '#dc3545',
                            color: 'white',
                            border: 'none',
                            padding: '10px 20px',
                            borderRadius: '4px',
                            cursor: 'pointer'
                        }}
                    >
                        Delete Cookie (Test Expiration)
                    </button>
                </div>

                <div style={{ 
                    background: '#d1ecf1', 
                    padding: '20px', 
                    borderRadius: '8px',
                    border: '1px solid #bee5eb'
                }}>
                    <h3>Expected Behavior</h3>
                    <p>When the security cookie expires or is deleted, you should be automatically redirected to <code>/account/overview</code> (or the page you came from) without needing to reload the page.</p>
                    <p>Check the browser console for detailed monitoring logs.</p>
                </div>
            </div>
        </AccountLayout>
    );
}
