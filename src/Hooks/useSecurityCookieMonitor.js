/**
 * Enhanced React Hook for Security Cookie Monitoring
 * 
 * Provides automatic security cookie monitoring with real-time expiration detection
 */

import { useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import securityCookieMonitor from '@/utils/securityCookieMonitor';

/**
 * Hook to monitor security cookies on secure pages with real-time expiration detection
 * 
 * @param {boolean} enabled - Whether to enable monitoring (default: true)
 * @param {Object} options - Configuration options
 * @param {string} options.fallbackUrl - URL to redirect to if cookie is invalid (default: '/account/overview')
 * @param {number} options.checkInterval - How often to check cookie in milliseconds (default: 5000)
 * @param {number} options.expiryMinutes - Expected cookie expiry time in minutes (default: 1)
 */
export function useSecurityCookieMonitor(enabled = true, options = {}) {
    const router = useRouter();
    const optionsRef = useRef(options);
    
    // Update options ref when options change
    useEffect(() => {
        optionsRef.current = options;
    }, [options]);

    useEffect(() => {
        if (!enabled) {
            return;
        }

        const {
            fallbackUrl = '/account/overview',
            checkInterval = 5000,
            expiryMinutes = 1
        } = optionsRef.current;

        // Configure monitor with options
        if (checkInterval) {
            securityCookieMonitor.checkInterval = checkInterval;
        }

        // Override redirect handler to use Next.js router
        const originalHandleInvalidCookie = securityCookieMonitor.handleInvalidCookie.bind(securityCookieMonitor);
        securityCookieMonitor.handleInvalidCookie = function(reason = 'Invalid or expired security cookie') {
            this.stopMonitoring();

            // Determine redirect URL
            const referrer = document.referrer;
            const currentPath = window.location.pathname;
            
            let redirectUrl = fallbackUrl;

            // Use referrer if it's from the same origin and not a security page
            if (referrer && this.isValidReferrer(referrer)) {
                try {
                    const referrerUrl = new URL(referrer);
                    if (referrerUrl.origin === window.location.origin) {
                        const referrerPath = referrerUrl.pathname + referrerUrl.search + referrerUrl.hash;
                        // Don't redirect to the same page we're currently on
                        if (referrerPath !== currentPath) {
                            redirectUrl = referrerPath;
                        }
                    }
                } catch (e) {
                    // Failed to parse referrer URL, use fallback
                }
            }

            // Show user-friendly message if in development
            if (this.debugMode) {
                console.warn(`[SecurityCookieMonitor] ${reason} - Redirecting from ${currentPath} to: ${redirectUrl}`);
            }

            // Use Next.js router for navigation
            router.push(redirectUrl);
        };

        // Start monitoring with expiry tracking
        securityCookieMonitor.startMonitoring(expiryMinutes);

        // Cleanup function
        return () => {
            securityCookieMonitor.stopMonitoring();
            // Restore original handler
            securityCookieMonitor.handleInvalidCookie = originalHandleInvalidCookie;
        };
    }, [enabled, router]);

    return {
        isMonitoring: securityCookieMonitor.isMonitoring,
        stopMonitoring: () => securityCookieMonitor.stopMonitoring(),
        startMonitoring: (expiryMinutes = 1) => securityCookieMonitor.startMonitoring(expiryMinutes),
        checkCookie: () => securityCookieMonitor.checkCookie()
    };
}

export default useSecurityCookieMonitor;
