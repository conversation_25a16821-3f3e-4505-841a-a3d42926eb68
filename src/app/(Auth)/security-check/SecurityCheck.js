"use client";
import { useRef, useState, useEffect } from "react";
import { ResendCodeIcon, ContactCustomerSupport } from "@/assets/svgIcons/SvgIcon";
import { securitySchema } from "@/validations/schema";
import { resendVerificationCode, verifyEmailToken } from "@/utils/auth";
import { Formik, Field, Form } from "formik";
import { useRouter, useSearchParams } from "next/navigation";
import CommonButton from "@/Components/UI/CommonButton";
import AuthLayout from "@/Layouts/AuthLayout";
import Link from "next/link";
import LoginFooter from "@/Components/UI/LoginFooter";
import AuthLogo from "@/Components/common/AuthLogo";
import MetaHead from "@/Seo/Meta/MetaHead";
import InputError from "@/Components/UI/InputError";
import toast from "react-hot-toast";
import { post } from "@/utils/apiUtils";
import { hashInput } from "@/utils/hashInput";
import "../../../css/common/textInput.scss"
import { v4 as uuidv4 } from "uuid";
import axios from "axios";


const initialValues = {
    security_code: "",
};

export default function SecurityCheck() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const inputRefs = useRef([]);
    const expiredOnceRef = useRef(false);

    const [isSessionValid, setIsSessionValid] = useState(null);
    const [maskedEmail, setMaskedEmail] = useState("");
    const [type, setType] = useState("");
    const [resendMessage, setResendMessage] = useState("");
    const [cooldown, setCooldown] = useState(0);
    const [isButtonDisabled, setIsButtonDisabled] = useState(false);
    const [isRotating, setIsRotating] = useState(false);
    const [securitySessionId, setSecuritySessionId] = useState(null);
    const [isCheckingExistingCookie, setIsCheckingExistingCookie] = useState(true);

    const provider = searchParams.get("provider");
    const resetPassword = searchParams.has("resetPassword");
    const nextUrl = searchParams.get("next"); // For account security verification
    const isAccountSecurity = !!nextUrl; // If next parameter exists, it's account security verification

    const dataType = resetPassword
        ? "reset_password_data"
        : provider
            ? `signup_${provider}`
            : "signup_data";

    // Function to check if security cookie is valid
    const isValidSecurityCookie = (cookieValue) => {
        if (!cookieValue) return false;

        // Simple validation for 'true' value
        if (cookieValue === 'true') return true;

        // Validate encrypted payload with timestamp
        try {
            const payload = JSON.parse(atob(cookieValue));
            if (payload.verified_at) {
                const verifiedAt = new Date(payload.verified_at);
                const now = new Date();
                const diffInSeconds = (now.getTime() - verifiedAt.getTime()) / 1000;
                // Check if within reasonable window (backend will do authoritative validation)
                // Use 15 minutes as a safe buffer since backend config may vary
                return diffInSeconds <= 900;
            }
        } catch (e) {
            return false;
        }
        return false;
    };

    // Function to get cookie value
    const getCookie = (name) => {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    };


    // ------------------------
    // Check for existing valid security cookie and redirect immediately
    useEffect(() => {
        if (isAccountSecurity && nextUrl) {
            const securityCookie = getCookie('security_verified');
            if (securityCookie && isValidSecurityCookie(securityCookie)) {
                console.log('Valid security cookie found, redirecting immediately to:', nextUrl);

                // Extract path from full URL if needed
                let redirectUrl = nextUrl;
                try {
                    if (redirectUrl.startsWith('http://') || redirectUrl.startsWith('https://')) {
                        const url = new URL(redirectUrl);
                        redirectUrl = url.pathname + url.search + url.hash;
                    }
                } catch (e) {
                    console.warn('Failed to parse next URL:', redirectUrl);
                }

                router.replace(redirectUrl);
                return;
            }
        }

        // If we reach here, no valid cookie found, so show the verification form
        setIsCheckingExistingCookie(false);
    }, [isAccountSecurity, nextUrl, router]);

    // ------------------------
    // Validate session
    useEffect(() => {
        const checkSessionValidity = () => {
            // For account security verification, skip session storage validation
            if (isAccountSecurity) {
                return true;
            }

            if (!dataType) return false;

            const savedData = JSON.parse(sessionStorage.getItem(dataType) || "{}");

            if ((!savedData.uuid || !savedData.expiresAt) && !expiredOnceRef.current) {
                expiredOnceRef.current = true;
                router.replace("/login");
                return false;
            }

            const expired = Date.now() > savedData.expiresAt;

            if (expired && !expiredOnceRef.current) {
                expiredOnceRef.current = true;
                sessionStorage.removeItem(dataType);
                sessionStorage.removeItem("masked_email");
                sessionStorage.removeItem("identifier_type");

                sessionStorage.setItem("sessionExpired", "true");
                // toast.error("Session expired. Please request a new code.");

                router.replace(resetPassword ? "/locate-account" : "/login");
                return false;
            }

            return true;
        };

        if (checkSessionValidity()) {
            setIsSessionValid(true);

            const interval = setInterval(checkSessionValidity, 5000);
            return () => clearInterval(interval);
        }
    }, [dataType, isAccountSecurity]);

    // ------------------------
    // Load masked email from sessionStorage or fetch from backend
    useEffect(() => {
        if (!isSessionValid) return;

        // For account security verification, send verification code immediately
        if (isAccountSecurity) {
            console.log('Account security verification detected. Next URL:', nextUrl);
            const sendSecurityCode = async () => {
                try {
                    console.log('Sending security verification code...');
                    const response = await post('/security-verification/send-code');
                    console.log('Send code response:', response);
                    if (response.success) {
                        setSecuritySessionId(response.session_id);
                        setMaskedEmail(response.masked_email || "your email");
                        setType("email");
                        console.log('Security session ID set:', response.session_id);
                    } else {
                        console.log("Failed to send verification code",response);
                        toast.error("Failed to send verification code");
                        router.replace("/account/overview");
                    }
                } catch (error) {
                    console.log("Failed to send verification code",error);
                    toast.error("Failed to send verification code");
                    router.replace("/account/overview");
                }
            };
            sendSecurityCode();
            return;
        }

        const masked = sessionStorage.getItem("masked_email");
        const identifierType = sessionStorage.getItem("identifier_type");

        if (masked) {
            setMaskedEmail(masked);
            if (identifierType) setType(identifierType);
            return;
        }

        if (dataType.startsWith("signup_") && dataType !== "signup_data") {
            const fetchMaskedEmail = async () => {
                const savedData = JSON.parse(sessionStorage.getItem(dataType) || "{}");
                const uuid = savedData.uuid;
                const expiresAt = savedData.expiresAt;

                if (!uuid || !expiresAt || Date.now() > expiresAt) {
                    toast.error("Signup session expired. Please start over.");
                    sessionStorage.removeItem(dataType);
                    sessionStorage.removeItem("masked_email");
                    sessionStorage.removeItem("identifier_type");
                    router.replace("/signup");
                    return;
                }

                const providerName = dataType.replace("signup_", "");
                console.log("Fetching email for:", providerName, uuid);

                try {
                    const response = await axios.get(
                        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/auth/signup-data/${providerName}/${uuid}`
                    );

                    console.log("Fetched signup-data:", response.data);

                    const { email } = response.data;

                    if (email) {
                        const masked = hashInput(email);
                        setMaskedEmail(masked);
                        sessionStorage.setItem("masked_email", masked);
                        sessionStorage.setItem("identifier_type", "email");
                    } else {
                        throw new Error("Email not found in response");
                    }
                } catch (error) {
                    if (error.response) {
                        console.error("Fetch failed:", error.response.status, error.response.data);
                    } else {
                        console.error("Network or Axios error:", error.message);
                    }
                    toast.error("Failed to retrieve your email. Please start over.");
                    // Optionally redirect: router.replace("/signup");
                }
            };

            fetchMaskedEmail();
        }
    }, [isSessionValid, dataType]);



    useEffect(() => {
        const storedCooldown = localStorage.getItem("resend_cooldown");
        let intervalId;

        if (storedCooldown) {
            const remainingTime = Math.floor((+storedCooldown - Date.now()) / 1000);
            if (remainingTime > 0) {
                intervalId = startCooldown(remainingTime);
            } else {
                intervalId = startCooldown(60);
            }
        } else {
            intervalId = startCooldown(60);
        }

        return () => clearInterval(intervalId);
    }, []);

    const startCooldown = (duration = 60) => {
        setIsButtonDisabled(true);
        setCooldown(duration);
        localStorage.setItem("resend_cooldown", Date.now() + duration * 1000);

        const interval = setInterval(() => {
            setCooldown(prev => {
                if (prev <= 1) {
                    clearInterval(interval);
                    setIsButtonDisabled(false);
                    localStorage.removeItem("resend_cooldown");
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);
        return interval;
    };

    const handleSubmit = async (values, { setSubmitting, setErrors }) => {
        // Handle account security verification
        if (isAccountSecurity) {
            if (!securitySessionId) {
                setErrors({ security_code: "Session expired. Please refresh the page." });
                return;
            }

            try {
                console.log('Sending verification request:', {
                    code: values.security_code,
                    session_id: securitySessionId,
                    next: nextUrl
                });

                const response = await post('/security-verification/verify-code', {
                    code: values.security_code,
                    session_id: securitySessionId,
                    next: nextUrl
                });

                console.log('Verification response:', response);

                if (response.success) {
                    let redirectUrl = response.redirect_url || nextUrl || "/account/overview";

                    // If redirectUrl is a full URL, extract just the path to avoid port/domain issues
                    try {
                        if (redirectUrl.startsWith('http://') || redirectUrl.startsWith('https://')) {
                            const url = new URL(redirectUrl);
                            redirectUrl = url.pathname + url.search + url.hash;
                        }
                    } catch (e) {
                        // If URL parsing fails, use the original redirectUrl
                        console.warn('Failed to parse redirect URL:', redirectUrl);
                    }

                    console.log('Verification successful! Redirecting to:', redirectUrl);
                    console.log('Current cookies before redirect:', document.cookie);

                    // Set the security cookie manually since backend cookie isn't working
                    const cookieValue = btoa(JSON.stringify({
                        user_id: 'verified',
                        verified_at: new Date().toISOString(),
                        session_id: securitySessionId
                    }));

                    // Use all cookie attributes from backend response (respects config)
                    const cookieConfig = response.cookie_config || {};
                    const expiresInMinutes = cookieConfig.expires_in_minutes || 1; // fallback to 1 minute
                    const expires = new Date(Date.now() + expiresInMinutes * 60 * 1000).toUTCString();

                    // Build cookie string with all attributes from backend config
                    let cookieString = `${cookieConfig.name || 'security_verified'}=${cookieValue}`;
                    cookieString += `; expires=${expires}`;
                    cookieString += `; path=${cookieConfig.path || '/'}`;

                    if (cookieConfig.domain) {
                        cookieString += `; domain=${cookieConfig.domain}`;
                    }

                    if (cookieConfig.secure) {
                        cookieString += `; Secure`;
                    }

                    if (cookieConfig.same_site) {
                        cookieString += `; SameSite=${cookieConfig.same_site}`;
                    }

                    // Note: HttpOnly cannot be set via JavaScript for security reasons
                    // The backend cookie should handle HttpOnly if needed

                    document.cookie = cookieString;

                    console.log(`Security cookie set with ${expiresInMinutes} minute(s) expiration and attributes:`, {
                        path: cookieConfig.path,
                        secure: cookieConfig.secure,
                        sameSite: cookieConfig.same_site,
                        domain: cookieConfig.domain
                    });;

                    console.log('Manually set security cookie');

                    // Use Next.js router for client-side navigation instead of page reload
                    setTimeout(() => {
                        console.log('Cookies after setting security cookie:', document.cookie);
                        console.log('Performing Next.js router push to:', redirectUrl);
                        router.push(redirectUrl);
                    }, 100);
                } else {
                    console.log('Verification failed:', response.message);
                    setErrors({ security_code: response.message || "Invalid verification code. Try again." });
                }
            } catch (error) {
                console.log('Verification error:', error);
                setErrors({ security_code: "Verification failed. Please try again." });
            }
            return;
        }

        // Original signup/reset password logic
        const savedData = JSON.parse(sessionStorage.getItem(dataType));

        if (!savedData || Date.now() > savedData.expiresAt) {
            setErrors({ security_code: "Token has expired. Please request a new one." });
            return;
        }

        const payload = {
            type: dataType,
            token: values.security_code,
            ...({ uuid: savedData.uuid })
        };

        const response = await verifyEmailToken(payload);

        if (response?.success) {
            const redirectTo = resetPassword
                ? "/change-password"
                : `/create-username${provider ? `?provider=${provider}` : ""}`;

            router.push(redirectTo);
        } else {
            setErrors({ security_code: response?.message || "Invalid verification code. Try again." });
        }
    };

    const handleResendClick = async (setErrors) => {
        if (isButtonDisabled) return;

        setIsRotating(true);
        startCooldown();

        // Handle account security verification resend
        if (isAccountSecurity) {
            if (!securitySessionId) {
                setErrors({ security_code: "Session expired. Please refresh the page." });
                setIsRotating(false);
                return;
            }

            try {
                const response = await post('/security-verification/resend-code', {
                    session_id: securitySessionId
                });

                if (response.success) {
                    setResendMessage(
                        "Verification code resent! Please check your email (including spam or junk folders) for the new code."
                    );
                } else {
                    setErrors({ security_code: response.message || "Failed to resend code." });
                }
            } catch (error) {
                setErrors({ security_code: "Failed to resend code. Please try again." });
            } finally {
                setIsRotating(false);
            }
            return;
        }


        const existingData = JSON.parse(sessionStorage.getItem(dataType)) || {};
        const oldUuid = existingData.uuid || uuidv4();

        try {
            const payload = { type: dataType, uuid: oldUuid };
            const response = await resendVerificationCode(payload);

            if (response.status === 429) {
                router.push(resetPassword ? "/locate-account" : "/signup");
                return;
            }

            if (!response.success) {
                setErrors({ security_code: response.message || "Something went wrong." });
                return;
            }

            // ✅ Cache does not exist → resend success
            const expiresInMinutes = 15;
            const expiresAt = Date.now() + expiresInMinutes * 60 * 1000;
            sessionStorage.setItem(dataType, JSON.stringify({ uuid: oldUuid, expiresAt }));

            setResendMessage(
                "Verification code resent! Please check your email (including spam or junk folders) for the new code. If you don’t receive it within a few minutes, try resending or contact our support team for assistance."
            );
        } catch (error) {
            console.error(error);
            setErrors({ security_code: "Something went wrong. Please try again." });
        } finally {
            setIsRotating(false);
        }
    };

    const handleChange = (e, index) => {
        const value = e.target.value;
        if (/^[a-zA-Z0-9]$/.test(value)) {
            if (index < inputRefs.current.length - 1) {
                inputRefs.current[index + 1].focus();
            }
        } else {
            e.target.value = "";
        }
    };

    const handleKeyDown = (e, index) => {
        if (e.key === "Backspace" && !e.target.value && index > 0) {
            inputRefs.current[index - 1].focus();
        }
    };

    const metaArray = {
        noindex: true,
        title: "Security Check | Verify Your TradeReply Account",
        description: "Enter your authorization code to verify your identity and securely access your TradeReply account. Protect your trading data with an extra layer of security.",
        canonical_link: "https://www.tradereply.com/security-check",
        og_site_name: "TradeReply",
        og_title: "Security Check | Verify Your TradeReply Account",
        og_description: "Enter your authorization code to verify your identity and securely access your TradeReply account. Protect your trading data with an extra layer of security.",
        twitter_title: "Security Check | Verify Your TradeReply Account",
        twitter_description: "Enter your authorization code to verify your identity and securely access your TradeReply account. Protect your trading data with an extra layer of security.",
    };

    // Show proper loading while checking for existing security cookie
    if (isCheckingExistingCookie && isAccountSecurity) {
        return (
            <AuthLayout>
                <MetaHead props={metaArray} />
                <div className="loginCommon_rightSide security_check">
                    <div className="loginCommon_rightSide_inner">
                        <div className="loginCommon_rightSide_formBox">
                            <AuthLogo />
                            <div className="loginHeading">
                                <h1>Security Check</h1>
                            </div>
                            <div className="text-center py-5">
                                <div className="security-check-loading">
                                    <div className="loading-spinner mb-3">
                                        <div className="spinner-border text-primary" role="status">
                                            <span className="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                    <h5 className="mb-2">Verifying Security Check</h5>
                                    <p className="text-white">Please wait while we verify your security status...</p>
                                </div>
                            </div>
                        </div>
                        <div className="mt-4 mt-md-5">
                            <LoginFooter />
                        </div>
                    </div>
                </div>
                <style jsx>{`
                    .security-check-loading {
                        padding: 2rem 0;
                    }
                    .loading-spinner {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }
                    .spinner-border {
                        width: 3rem;
                        height: 3rem;
                        border-width: 0.3em;
                    }
                    .text-primary {
                        color: #007bff !important;
                    }
                    .text-muted {
                        color: #6c757d !important;
                        font-size: 0.9rem;
                    }
                    .visually-hidden {
                        position: absolute !important;
                        width: 1px !important;
                        height: 1px !important;
                        padding: 0 !important;
                        margin: -1px !important;
                        overflow: hidden !important;
                        clip: rect(0, 0, 0, 0) !important;
                        white-space: nowrap !important;
                        border: 0 !important;
                    }
                `}</style>
            </AuthLayout>
        );
    }

    // Show professional loading screen for signup/reset password flows
    if (isSessionValid === null) {
        return (
            <AuthLayout>
                <MetaHead props={metaArray} />
                <div className="loginCommon_rightSide security_check">
                    <div className="loginCommon_rightSide_inner">
                        <div className="loginCommon_rightSide_formBox">
                            <AuthLogo />
                            <div className="loginHeading">
                                <h1>Security Check</h1>
                            </div>
                            <div className="text-center py-5">
                                <div className="security-check-loading">
                                    <div className="loading-spinner mb-3">
                                        <div className="spinner-border text-primary" role="status">
                                            <span className="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                    <h5 className="mb-2">Verifying Your Information</h5>
                                    <p className="text-white">
                                        {resetPassword
                                            ? "Please wait while we verify your account details..."
                                            : "Please wait while we verify your signup details..."
                                        }
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="mt-4 mt-md-5">
                            <LoginFooter />
                        </div>
                    </div>
                </div>
                <style jsx>{`
                    .security-check-loading {
                        padding: 2rem 0;
                    }
                    .loading-spinner {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }
                    .spinner-border {
                        width: 3rem;
                        height: 3rem;
                        border-width: 0.3em;
                    }
                    .text-primary {
                        color: #007bff !important;
                    }
                    .text-muted {
                        color: #6c757d !important;
                        font-size: 0.9rem;
                    }
                    .visually-hidden {
                        position: absolute !important;
                        width: 1px !important;
                        height: 1px !important;
                        padding: 0 !important;
                        margin: -1px !important;
                        overflow: hidden !important;
                        clip: rect(0, 0, 0, 0) !important;
                        white-space: nowrap !important;
                        border: 0 !important;
                    }
                `}</style>
            </AuthLayout>
        );
    }

    if (isSessionValid === false) {
        return null;
    }

    return (
        <AuthLayout>
            <MetaHead props={metaArray} />
            <div className="loginCommon_rightSide security_check">
                <div className="loginCommon_rightSide_inner">
                    <div className="loginCommon_rightSide_formBox">
                        <AuthLogo />
                        <div className="loginHeading">
                            <h1>Security Check</h1>
                        </div>
                        <div id="auth-code-label">
                            <div className="text-center pt-3">
                                <div className="text-center pt-3">
                                    {isAccountSecurity ? (
                                        <span className="font-semibold">
                                            For your security, please enter the verification code sent to your email address to continue.
                                        </span>
                                    ) : dataType === "reset_password_data" ? (
                                        <>
                                            {type === "email" || !type ? (
                                                <span className="font-semibold">
                                                    {type === "email" || !type
                                                        ? "If an account with the entered email or username exists, a verification code has been sent."
                                                        : <>Please enter the verification code sent to <strong>{maskedEmail}</strong>.</>
                                                    }
                                                </span>
                                            ) : (
                                                <span className="font-semibold">
                                                    Please enter the verification code sent to the email associated with <strong>{maskedEmail}</strong>. If you don't receive an email, check your spam folder or contact support for assistance.
                                                </span>
                                            )}
                                        </>
                                    ) : (
                                        <span>Please enter the verification code sent to:</span>
                                    )}
                                </div>

                            </div>
                            <div className="text-center py-3 user_email">
                                <span>{maskedEmail}</span>
                            </div>
                        </div>
                        <div className="loginTabs">
                            <div className="loginForm">
                                <Formik
                                    initialValues={initialValues}
                                    validationSchema={securitySchema}
                                    onSubmit={handleSubmit}
                                >
                                    {({ isSubmitting }) => (
                                        <Form>
                                            <Field name="security_code">
                                                {({ field, form, meta }) => {
                                                    const handlePaste = (e) => {
                                                        e.preventDefault();
                                                        const pasteData = e.clipboardData.getData("Text");
                                                        const cleaned = pasteData.replace(/[^a-zA-Z0-9]/g, "");
                                                        const chars = cleaned.split("").slice(0, 6);
                                                        let newCode = new Array(6).fill("");
                                                        chars.forEach((char, i) => {
                                                            newCode[i] = char;
                                                            if (inputRefs.current[i]) {
                                                                inputRefs.current[i].value = char;
                                                            }
                                                        });
                                                        form.setFieldValue(field.name, newCode.join(""));
                                                        if (chars.length < inputRefs.current.length) {
                                                            inputRefs.current[chars.length].focus();
                                                        }
                                                    };

                                                    const handleResend = () => {
                                                        inputRefs.current.forEach((input) => {
                                                            if (input) input.value = "";
                                                        });

                                                        form.setFieldValue("security_code", "");
                                                        form.setFieldError("security_code", "");

                                                        handleResendClick({
                                                            security_code: () => {
                                                                form.setFieldError("security_code", "Failed to resend code.");
                                                            },
                                                        });

                                                        inputRefs.current[0]?.focus();
                                                    };

                                                    return (
                                                        <>
                                                            <div className="py-3 security_check_input">
                                                                <div role="group" aria-labelledby="auth-code-label">
                                                                    <div className="d-flex justify-content-between">
                                                                        {Array.from({ length: 6 }).map((_, index) => {
                                                                            const charValue = field.value ? field.value[index] || "" : "";
                                                                            return (
                                                                                <input
                                                                                    key={index}
                                                                                    type="text"
                                                                                    maxLength="1"
                                                                                    id={`auth-code-${index + 1}`}
                                                                                    inputMode="text"
                                                                                    aria-label={`${["First", "Second", "Third", "Fourth", "Fifth", "Sixth"][index]} character`}
                                                                                    value={charValue}
                                                                                    onChange={(e) => {
                                                                                        const val = e.target.value;
                                                                                        let newCode = field.value
                                                                                            ? field.value.split("")
                                                                                            : new Array(6).fill("");
                                                                                        newCode[index] = val;
                                                                                        form.setFieldValue(field.name, newCode.join(""));
                                                                                        handleChange(e, index);
                                                                                    }}
                                                                                    onKeyDown={(e) => handleKeyDown(e, index)}
                                                                                    onPaste={handlePaste}
                                                                                    ref={(el) => (inputRefs.current[index] = el)}
                                                                                    className={meta.touched && meta.error ? "error-field" : ""}
                                                                                />
                                                                            );
                                                                        })}
                                                                    </div>
                                                                    {meta.touched && meta.error ? (
                                                                        <InputError message={meta.error} />
                                                                    ) : null}
                                                                </div>
                                                            </div>

                                                            <div className="pb-3 d-flex justify-content-center">
                                                                <button
                                                                    type="button"
                                                                    className="security_check_resend_btn d-flex align-items-center gap-3"
                                                                    onClick={handleResend}
                                                                    disabled={isButtonDisabled}
                                                                >
                                                                    Resend Code
                                                                    <ResendCodeIcon isRotating={isRotating} />
                                                                    <span>{cooldown > 0 ? `${cooldown}s` : ""}</span>
                                                                </button>
                                                            </div>
                                                        </>
                                                    );
                                                }}
                                            </Field>
                                            {resendMessage && (
                                                <div className="text-center text-success mt-3 mb-6 px-3">
                                                    <p>{resendMessage}</p>
                                                </div>
                                            )}
                                            <div className="w-100">
                                                <CommonButton
                                                    type="submit"
                                                    title={isSubmitting ? "Loading" : "Continue"}
                                                    fluid
                                                    disabled={isSubmitting}
                                                />
                                            </div>
                                            <Link href={isAccountSecurity ? "/account/overview" : "/login"} className="w-100 mt-3">
                                                <CommonButton title="Cancel" white20 />
                                            </Link>
                                            <div className="anAccount mt-3 d-flex justify-content-center">
                                                <Link href="/help" className="d-flex align-items-center gap-2">
                                                    <span className="font-bold">Contact Customer Support</span>
                                                    <ContactCustomerSupport />
                                                </Link>
                                            </div>
                                        </Form>
                                    )}
                                </Formik>
                            </div>
                        </div>
                    </div>
                    <div className="mt-4 mt-md-5">
                        <LoginFooter />
                    </div>
                </div>
            </div>
        </AuthLayout>
    );
}