<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Security Verification Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the security verification
    | system including cookie settings, expiration times, and security options.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Cookie Configuration
    |--------------------------------------------------------------------------
    |
    | Basic cookie settings with sensible defaults and minimal customization.
    |
    */

    'cookie' => [
        // Cookie name for security verification
        'name' => 'security_verified',

        // Cookie expiration time in minutes
        'expires_minutes' => 1,

        // Cookie path
        'path' => '/',

        // HTTP Only flag (false so frontend can read it)
        'http_only' => false,

        // SameSite attribute
        'same_site' => 'Lax',
    ],

    /*
    |--------------------------------------------------------------------------
    | Cryptographic Security
    |--------------------------------------------------------------------------
    |
    | Enable cryptographic signing to prevent cookie tampering.
    |
    */

    'crypto' => [
        // Enable cryptographic signing of cookie values
        'sign_cookies' => true,

        // Algorithm for signing
        'signing_algorithm' => 'sha256',
    ],

    /*
    |--------------------------------------------------------------------------
    | Session Configuration
    |--------------------------------------------------------------------------
    |
    | Basic session and rate limiting settings.
    |
    */

    'session' => [
        // Verification session cache expiration in minutes
        'cache_expires_minutes' => 15,

        // Rate limiting for resend requests in seconds
        'resend_rate_limit_seconds' => 60,

        // Maximum verification attempts before lockout
        'max_attempts' => 5,

        // Lockout duration in minutes after max attempts
        'lockout_minutes' => 15,
    ],

    /*
    |--------------------------------------------------------------------------
    | Protected Routes
    |--------------------------------------------------------------------------
    |
    | List of routes that require security verification.
    |
    */

    'protected_routes' => [
        '/account/phone/setup',
        '/account/email/setup',
        '/account/password/change',
        '/account/2fa/setup',
        '/account/create-username',
        '/account/address/manage',
        '/account/address/setup',
    ],

    /*
    |--------------------------------------------------------------------------
    | Environment Detection
    |--------------------------------------------------------------------------
    |
    | Simple environment detection for cookie domain and security settings.
    |
    */

    'environment' => [
        // Development domains (for auto-detection)
        'development_domains' => [
            'localhost',
            '127.0.0.1',
            '::1',
            '.local',
            '.dev',
            '.test',
        ],

        // Production domains (for auto-detection)
        'production_domains' => [
            'tradereply.com',
            '.tradereply.com',
        ],
    ],

];
