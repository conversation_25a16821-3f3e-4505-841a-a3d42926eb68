/**
 * Enhanced Security Cookie Monitoring Utility
 * 
 * Monitors security verification cookies on secure pages and automatically
 * redirects users when cookies become invalid or expire in real-time.
 */

class SecurityCookieMonitor {
    constructor() {
        this.intervalId = null;
        this.isMonitoring = false;
        this.checkInterval = 5000; // 5 seconds for responsive detection
        this.cookieName = 'security_verified';
        this.debugMode = process.env.NODE_ENV === 'development';
        this.lastCookieValue = null;
        this.cookieSetTime = null;
        this.expectedExpiryTime = null;
        this.expiryWarningShown = false;
    }

    /**
     * Start monitoring security cookies with expiration tracking
     */
    startMonitoring(expiryMinutes = 1) {
        if (this.isMonitoring) {
            this.log('Cookie monitoring already active');
            return;
        }

        this.log('Starting enhanced security cookie monitoring');
        this.isMonitoring = true;
        this.expiryWarningShown = false;

        // Track when monitoring starts and expected expiry
        this.cookieSetTime = new Date();
        this.expectedExpiryTime = new Date(this.cookieSetTime.getTime() + (expiryMinutes * 60 * 1000));

        this.log('Cookie expiry tracking:', {
            setTime: this.cookieSetTime.toISOString(),
            expectedExpiry: this.expectedExpiryTime.toISOString(),
            expiryMinutes: expiryMinutes
        });

        // Initial check
        this.checkCookie();

        // Set up interval checking
        this.intervalId = setInterval(() => {
            this.checkCookie();
        }, this.checkInterval);

        // Listen for page visibility changes
        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
        
        // Listen for beforeunload to cleanup
        window.addEventListener('beforeunload', this.stopMonitoring.bind(this));

        // Set up expiry countdown
        this.setupExpiryCountdown(expiryMinutes);
    }

    /**
     * Set up countdown to expected expiry time
     */
    setupExpiryCountdown(expiryMinutes) {
        const expiryTime = expiryMinutes * 60 * 1000; // Convert to milliseconds
        
        // Check every second near expiry time
        const countdownInterval = setInterval(() => {
            const now = new Date();
            const timeElapsed = now.getTime() - this.cookieSetTime.getTime();
            const timeRemaining = expiryTime - timeElapsed;

            if (timeRemaining <= 0) {
                this.log('Cookie should have expired based on timing, forcing check');
                clearInterval(countdownInterval);
                this.checkCookie();
                return;
            }

            // Show warning when 30 seconds remain
            if (timeRemaining <= 30000 && !this.expiryWarningShown) {
                this.expiryWarningShown = true;
                this.log('Cookie expiring soon:', {
                    timeRemaining: Math.round(timeRemaining / 1000) + ' seconds'
                });
            }

            // Log countdown in debug mode
            if (this.debugMode && timeRemaining <= 60000) { // Last minute
                this.log(`Cookie expires in ${Math.round(timeRemaining / 1000)} seconds`);
            }

        }, 1000); // Check every second

        // Store interval for cleanup
        this.countdownInterval = countdownInterval;
    }

    /**
     * Stop monitoring security cookies
     */
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }

        this.log('Stopping security cookie monitoring');
        this.isMonitoring = false;

        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }

        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }

        // Remove event listeners
        document.removeEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
        window.removeEventListener('beforeunload', this.stopMonitoring.bind(this));
    }

    /**
     * Check cookie validity and handle redirects
     */
    checkCookie() {
        try {
            const cookieValue = this.getCookie(this.cookieName);
            const isValid = this.isValidSecurityCookie(cookieValue);

            // Check if cookie disappeared
            const cookieDisappeared = this.lastCookieValue && !cookieValue;
            
            // Check if cookie changed (possible tampering)
            const cookieChanged = this.lastCookieValue && cookieValue && this.lastCookieValue !== cookieValue;

            this.log('Cookie check result:', {
                cookieExists: !!cookieValue,
                cookiePreview: cookieValue ? cookieValue.substring(0, 8) + '...' : 'none',
                isValid: isValid,
                cookieDisappeared: cookieDisappeared,
                cookieChanged: cookieChanged,
                timestamp: new Date().toISOString()
            });

            // Update last known cookie value
            this.lastCookieValue = cookieValue;

            if (!isValid || cookieDisappeared) {
                let reason = 'Invalid or missing security cookie';
                if (cookieDisappeared) reason = 'Security cookie was deleted or expired';
                if (cookieChanged) reason = 'Security cookie was modified';
                
                this.log('Cookie validation failed:', reason);
                this.handleInvalidCookie(reason);
            }

        } catch (error) {
            this.log('Error during cookie check:', error);
            this.handleInvalidCookie('Cookie check error: ' + error.message);
        }
    }

    /**
     * Handle invalid cookie by redirecting user
     */
    handleInvalidCookie(reason = 'Invalid or expired security cookie') {
        this.stopMonitoring();

        // Determine redirect URL
        const referrer = document.referrer;
        const fallbackUrl = '/account/overview';
        
        let redirectUrl = fallbackUrl;

        // Use referrer if it's from the same origin and not a security page
        if (referrer && this.isValidReferrer(referrer)) {
            try {
                const referrerUrl = new URL(referrer);
                if (referrerUrl.origin === window.location.origin) {
                    redirectUrl = referrerUrl.pathname + referrerUrl.search + referrerUrl.hash;
                }
            } catch (e) {
                this.log('Failed to parse referrer URL:', e);
            }
        }

        this.log('Redirecting due to invalid cookie:', {
            reason: reason,
            referrer: referrer,
            redirectUrl: redirectUrl,
            currentPage: window.location.pathname
        });

        // Show user-friendly message if in development
        if (this.debugMode) {
            console.warn(`[SecurityCookieMonitor] ${reason} - Redirecting to: ${redirectUrl}`);
        }

        // Use Next.js router if available, otherwise use window.location
        if (typeof window !== 'undefined' && window.next && window.next.router) {
            window.next.router.push(redirectUrl);
        } else {
            window.location.href = redirectUrl;
        }
    }

    /**
     * Check if referrer is valid for redirect
     */
    isValidReferrer(referrer) {
        if (!referrer) return false;

        // Don't redirect back to security-related pages
        const securityPages = ['/security-check', '/login', '/signup', '/verify-email'];
        
        for (const page of securityPages) {
            if (referrer.includes(page)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Handle page visibility changes
     */
    handleVisibilityChange() {
        if (document.hidden) {
            this.log('Page hidden, pausing cookie monitoring');
            if (this.intervalId) {
                clearInterval(this.intervalId);
                this.intervalId = null;
            }
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval);
                this.countdownInterval = null;
            }
        } else {
            this.log('Page visible, resuming cookie monitoring');
            if (this.isMonitoring && !this.intervalId) {
                this.intervalId = setInterval(() => {
                    this.checkCookie();
                }, this.checkInterval);
                
                // Restart countdown if we have expiry time
                if (this.expectedExpiryTime) {
                    const now = new Date();
                    const timeRemaining = this.expectedExpiryTime.getTime() - now.getTime();
                    if (timeRemaining > 0) {
                        this.setupExpiryCountdown(timeRemaining / (60 * 1000)); // Convert back to minutes
                    }
                }
            }
        }
    }

    /**
     * Get cookie value by name
     */
    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) {
            return parts.pop().split(';').shift();
        }
        return null;
    }

    /**
     * Validate security cookie (same logic as SecurityCheck component)
     */
    isValidSecurityCookie(cookieValue) {
        if (!cookieValue) return false;

        // Simple validation for 'true' value (legacy)
        if (cookieValue === 'true') return true;

        // Check if it's a session token (64 character hex string)
        if (typeof cookieValue === 'string' && cookieValue.length === 64 && /^[a-f0-9]+$/i.test(cookieValue)) {
            return true;
        }

        // Validate old encrypted payload format for backward compatibility
        try {
            const payload = JSON.parse(atob(cookieValue));
            if (payload.verified_at) {
                const verifiedAt = new Date(payload.verified_at);
                const now = new Date();
                const diffInSeconds = (now.getTime() - verifiedAt.getTime()) / 1000;
                return diffInSeconds <= 900; // 15 minutes buffer
            }
        } catch (e) {
            return false;
        }
        return false;
    }

    /**
     * Log messages (only in debug mode)
     */
    log(message, data = null) {
        if (this.debugMode) {
            if (data) {
                console.log(`[SecurityCookieMonitor] ${message}`, data);
            } else {
                console.log(`[SecurityCookieMonitor] ${message}`);
            }
        }
    }
}

// Create singleton instance
const securityCookieMonitor = new SecurityCookieMonitor();

export default securityCookieMonitor;
