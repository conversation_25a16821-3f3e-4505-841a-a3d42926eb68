# Security Cookie Monitoring System - Implementation Guide

## Overview

The enhanced security verification system now includes **real-time client-side cookie monitoring** that automatically detects when security cookies expire and redirects users back to their previous page without requiring a page reload.

## Problem Solved

**Before**: Users on secure pages would only be redirected when they manually reloaded the page or navigated to another page after their security cookie expired.

**After**: Users are automatically redirected in real-time when their security cookie expires, providing a seamless user experience.

## How It Works

### 1. Real-Time Monitoring
- Checks security cookie every **5 seconds** (configurable)
- Tracks expected expiry time based on backend configuration
- Provides countdown warnings in development mode
- Detects cookie deletion, modification, or natural expiration

### 2. Smart Redirection
- Redirects to the **referrer page** (where user came from) when possible
- Falls back to `/account/overview` if no valid referrer
- Avoids redirect loops by validating referrer URLs
- Uses Next.js router for smooth client-side navigation

### 3. Session-Based Security
- Uses **64-character session tokens** instead of full payload cookies
- Database-backed validation with IP and user agent checking
- Automatic cleanup of expired sessions
- Backward compatibility with old cookie format

## Implementation

### For New Secure Pages

Add the security monitoring hook to any page that requires security verification:

```javascript
'use client';
import { useSecurityCookieMonitor } from "@/hooks/useSecurityCookieMonitor";

export default function YourSecurePage() {
    // Enable security cookie monitoring
    useSecurityCookieMonitor(true, {
        fallbackUrl: '/account/overview', // Where to redirect if no valid referrer
        expiryMinutes: 1 // Match your backend configuration
    });

    return (
        <div>Your secure content here</div>
    );
}
```

### For Existing Secure Pages

Simply add the import and hook call to existing secure pages:

```javascript
// Add this import
import { useSecurityCookieMonitor } from "@/hooks/useSecurityCookieMonitor";

// Add this hook call inside your component
useSecurityCookieMonitor(true, {
    fallbackUrl: '/account/overview',
    expiryMinutes: 1
});
```

### Configuration Options

```javascript
useSecurityCookieMonitor(enabled, {
    fallbackUrl: '/account/overview',    // Default redirect URL
    checkInterval: 5000,                 // Check every 5 seconds
    expiryMinutes: 1                     // Expected cookie lifetime
});
```

## Testing the System

### Test Page Available

Visit `/account/test-security` to see the monitoring system in action:

1. **Real-time cookie status** - Shows if cookie exists and is valid
2. **Countdown timer** - Shows estimated time until expiration
3. **Manual testing** - Buttons to force cookie checks or deletion
4. **Automatic redirect** - Demonstrates the redirect behavior

### Manual Testing Steps

1. **Natural Expiration Test**:
   - Go to any secure page (e.g., `/account/phone/setup`)
   - Wait 1 minute (or your configured expiry time)
   - You should be automatically redirected without page reload

2. **Cookie Deletion Test**:
   - Go to `/account/test-security`
   - Click "Delete Cookie (Test Expiration)"
   - You should be immediately redirected

3. **Referrer Test**:
   - Start at `/account/overview`
   - Click "Add Phone Number" to go to `/account/phone/setup`
   - Wait for cookie to expire or delete it manually
   - You should be redirected back to `/account/overview`

## Current Secure Pages

The following pages automatically have security monitoring enabled:

- `/account/phone/setup` ✅ (Updated)
- `/account/email/setup` (Needs update)
- `/account/password/change` (Needs update)
- `/account/2fa/setup` (Needs update)
- `/account/create-username` (Needs update)
- `/account/address/manage` (Needs update)
- `/account/address/setup` (Needs update)
- `/account/test-security` ✅ (Test page)

## Backend Configuration

### Cookie Expiration

Set in `config/security.php`:

```php
'cookie' => [
    'expires_minutes' => 1, // Cookie lifetime in minutes
    // ... other cookie settings
],
```

### Protected Routes

Add new secure pages to `config/security.php`:

```php
'protected_routes' => [
    '/account/phone/setup',
    '/account/your-new-page', // Add new secure pages here
    // ...
],
```

And to `frontend/src/middleware.js`:

```javascript
const securityProtectedRoutes = [
    '/account/phone/setup',
    '/account/your-new-page', // Add new secure pages here
    // ...
];
```

## Development Features

### Debug Logging

In development mode, the system provides comprehensive console logging:

- Cookie validation results
- Expiration countdown
- Redirect decisions
- Monitoring status changes

### Visual Indicator

The `SecurePageWrapper` component shows a monitoring status indicator in development mode.

## Database Management

### Session Cleanup

Run the cleanup command to remove expired sessions:

```bash
php artisan security:cleanup-sessions
```

### Monitoring Sessions

Check active sessions in the `security_verification_sessions` table:

```sql
SELECT user_id, session_token, expires_at, ip_address 
FROM security_verification_sessions 
WHERE expires_at > NOW();
```

## Troubleshooting

### Common Issues

1. **Monitoring not starting**: Check that the hook is called inside a React component
2. **Redirects not working**: Verify Next.js router is available and routes are correct
3. **Cookie not expiring**: Check backend configuration and ensure times match
4. **Console errors**: Check import paths and ensure all files are created

### Debug Steps

1. Open browser console to see monitoring logs
2. Check Network tab for API calls during verification
3. Verify cookie exists in Application/Storage tab
4. Test with `/account/test-security` page

## Security Considerations

### Client-Side Validation

- Client-side validation is **not authoritative**
- Backend always performs the final security validation
- Client-side monitoring is for **user experience only**

### Session Security

- Session tokens are cryptographically secure
- IP address and user agent validation prevent session hijacking
- Automatic cleanup prevents database bloat
- Rate limiting prevents abuse

## Performance Impact

### Minimal Overhead

- Monitoring checks every 5 seconds (configurable)
- Lightweight cookie parsing and validation
- Automatic pause when page is hidden
- Proper cleanup prevents memory leaks

### Optimization

- Uses singleton pattern for monitor instance
- Efficient cookie parsing with regex validation
- Minimal DOM manipulation
- Conditional logging based on environment

## Future Enhancements

### Possible Improvements

1. **WebSocket integration** for real-time server notifications
2. **Progressive expiration warnings** (30s, 10s, 5s warnings)
3. **Automatic session extension** for active users
4. **Cross-tab synchronization** for multi-tab scenarios
5. **Offline detection** and handling

This system provides a robust, user-friendly security monitoring solution that enhances the user experience while maintaining strong security standards.
