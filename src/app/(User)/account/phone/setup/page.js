'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState, useEffect } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import TextInput from "@/Components/UI/TextInput";
import { post, get } from "@/utils/apiUtils";
import { useRouter, useSearchParams } from "next/navigation";
import { useSelector } from 'react-redux';
import { CheckIcon, RedCircleCrossIcon, GreyCheckIcon } from '@/assets/svgIcons/SvgIcon';
import { useSecurityCookieMonitor } from "@/hooks/useSecurityCookieMonitor";
import "@/css/account/AccountDetails.scss";

export default function SetupPhoneNumber() {
    const [phoneNumber, setPhoneNumber] = useState('');
    const [originalPhoneNumber, setOriginalPhoneNumber] = useState('');
    const [smsUpdates, setSmsUpdates] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingUserData, setIsLoadingUserData] = useState(true);
    const [saveStatus, setSaveStatus] = useState(null); // 'loading', 'success', 'error', null
    const [error, setError] = useState(null);
    const router = useRouter();
    const searchParams = useSearchParams();
    const reduxUser = useSelector((state) => state?.auth?.user || null);

    // Enable security cookie monitoring for this secure page
    useSecurityCookieMonitor(true, {
        fallbackUrl: '/account/overview',
        expiryMinutes: 1 // Match the backend configuration
    });

    // Fetch user data to pre-populate phone number
    const fetchUserData = async () => {
        try {
            setIsLoadingUserData(true);
            const response = await get('/account');

            if (response.success && response.data) {
                const existingPhone = response.data.phone_number || '';
                setPhoneNumber(existingPhone);
                setOriginalPhoneNumber(existingPhone);
            }
        } catch (error) {
            console.error('Error fetching user data:', error);
            // Fallback to Redux user data
            if (reduxUser?.phone_number) {
                setPhoneNumber(reduxUser.phone_number);
                setOriginalPhoneNumber(reduxUser.phone_number);
            }
        } finally {
            setIsLoadingUserData(false);
        }
    };

    useEffect(() => {
        fetchUserData();
    }, []);

    const handlePhoneChange = (e) => {
        const value = e.target.value;
        if (/^\+?\d*$/.test(value)) {
            setPhoneNumber(value);
        }
    };

    const isUpdating = originalPhoneNumber.length > 0;

    // Determine redirect URL based on referrer or URL parameters
    const getRedirectUrl = () => {
        // Check for explicit redirect parameter in URL
        const redirectParam = searchParams.get('redirect');
        if (redirectParam) {
            try {
                // Decode the redirect parameter
                return decodeURIComponent(redirectParam);
            } catch (e) {
                console.warn('Failed to decode redirect parameter:', redirectParam);
            }
        }

        // Check for referrer from URL parameter (for programmatic navigation)
        const fromParam = searchParams.get('from');
        if (fromParam) {
            return fromParam;
        }

        // Check document.referrer for natural navigation
        if (typeof window !== 'undefined' && document.referrer) {
            try {
                const referrerUrl = new URL(document.referrer);
                const currentUrl = new URL(window.location.href);

                // Only use referrer if it's from the same origin (security)
                if (referrerUrl.origin === currentUrl.origin) {
                    const referrerPath = referrerUrl.pathname + referrerUrl.search + referrerUrl.hash;

                    // Don't redirect back to the phone setup page itself
                    if (!referrerPath.includes('/account/phone/setup')) {
                        return referrerPath;
                    }
                }
            } catch (e) {
                console.warn('Failed to parse referrer URL:', document.referrer);
            }
        }

        // Default fallback
        return '/account/overview';
    };

    // Status indicator component (matches PersonalInformation exactly)
    const StatusIndicator = () => {
        if (saveStatus === 'loading') {
            return (
                <div className="status_indicator status-loading">
                    <img
                        src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-saving.svg"
                        alt="Saving Icon"
                    />
                    <span>Saving...</span>
                </div>
            );
        }

        if (saveStatus === 'success') {
            return (
                <div className="status_indicator status-success">
                    <CheckIcon />
                    <span>Auto-saved</span>
                </div>
            );
        }

        if (saveStatus === 'error') {
            return (
                <div className="status_indicator status-error">
                    <RedCircleCrossIcon />
                    <span>{error || 'Unable to save'}</span>
                </div>
            );
        }

        return (
            <div className="status_indicator status-default">
                <GreyCheckIcon />
                <span>All changes saved</span>
            </div>
        );
    };

    const handleSave = async () => {
        if (!phoneNumber.trim()) {
            setSaveStatus('error');
            setError('Phone number is required');
            setTimeout(() => setSaveStatus(null), 3000);
            return;
        }

        try {
            setIsLoading(true);
            setSaveStatus('loading');
            setError(null);

            const response = await post('/phone/setup', {
                phone_number: phoneNumber,
            });

            if (response.success) {
                setSaveStatus('success');

                // Get the appropriate redirect URL
                const redirectUrl = getRedirectUrl();
                console.log('Phone setup success - redirecting to:', redirectUrl);

                // Redirect back to the original page after 2 seconds
                setTimeout(() => {
                    router.push(redirectUrl);
                }, 2000);
            } else {
                throw new Error(response.message || 'Failed to save phone number');
            }
        } catch (err) {
            console.error('Phone setup error:', err);
            const errorMessage = err.response?.data?.message || err.message || 'Failed to save phone number. Please try again.';
            setSaveStatus('error');
            setError(errorMessage);
        } finally {
            setIsLoading(false);
            setTimeout(() => setSaveStatus(null), 3000);
        }
    };

    const handleCancel = () => {
        const redirectUrl = getRedirectUrl();
        router.push(redirectUrl);
    };

    const metaArray = {
        noindex: true,
        title: `${isUpdating ? 'Update' : 'Setup'} Phone Number | Update Info | TradeReply`,
        description: `${isUpdating ? 'Update' : 'Add'} your phone number on TradeReply.com.`,
        canonical_link: "https://www.tradereply.com/account/details",
        og_site_name: "TradeReply",
        og_title: `${isUpdating ? 'Update' : 'Setup'} Phone Number | Update Info | TradeReply`,
        og_description: `${isUpdating ? 'Update' : 'Add'} your phone number on TradeReply.com.`,
        twitter_title: `${isUpdating ? 'Update' : 'Setup'} Phone Number | Update Info | TradeReply`,
        twitter_description: `${isUpdating ? 'Update' : 'Add'} your phone number on TradeReply.com.`,
    };
    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_setup_phone_number">
                    <SidebarHeading title={isUpdating ? "Update Phone Number" : "New Phone Number"} />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <div className="account_header_main">
                                        <h6>Phone Number</h6>
                                        <div className="account_status_indicator">
                                            <StatusIndicator />
                                        </div>
                                    </div>
                                    <p>Provide your number to receive occasional updates, exclusive offers, or important notifications. We will never share your number without consent.</p>
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <div className="account_card_list">
                                    {isLoadingUserData ? (
                                        <div className="col-12 text-center py-3">
                                            <span>Loading current phone number...</span>
                                        </div>
                                    ) : (
                                        <div className="col-lg-5 col-md-8 col-12">
                                            <TextInput
                                                type="text"
                                                placeholder="Enter your phone number"
                                                value={phoneNumber}
                                                onChange={handlePhoneChange}
                                                inputMode="numeric"
                                            />
                                        </div>
                                    )}
                                    <div className="col-md-8 col-12">
                                        <label
                                            className="d-flex gap-3"
                                            htmlFor="sms-product"
                                        >
                                            <input
                                                className="custom_checkbox_input form-check-input"
                                                type="checkbox"
                                                id="sms-product"
                                                checked={smsUpdates}
                                                onChange={(e) => setSmsUpdates(e.target.checked)}
                                            />
                                            <div>
                                                <h6>SMS Product Updates & Offers</h6>
                                                <p>Receive text messages with feature announcements, product updates, and occasional offers from TradeReply. By enabling this, you agree to receive recurring marketing texts at the number provided. Consent is not a condition of purchase. Message and data rates may apply. Text STOP to opt out, or HELP for help.</p>
                                            </div>
                                        </label>
                                    </div>

                                    {error && saveStatus !== 'loading' && (
                                        <div className="mt-3">
                                            <p style={{ color: 'red', fontSize: '14px' }}>
                                                {error}
                                            </p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className="account_card_list_btns mt-3">
                            <button
                                className="btn-style white-btn"
                                onClick={handleCancel}
                                disabled={isLoading || isLoadingUserData}
                            >
                                Cancel
                            </button>
                            <button
                                className="btn-style"
                                onClick={handleSave}
                                disabled={isLoading || isLoadingUserData || !phoneNumber.trim()}
                            >
                                {isLoading ? "Saving..." : isUpdating ? "Update Phone Number" : "Save Phone Number"}
                            </button>
                        </div>
                    </div>

                </div>
            </AccountLayout>
        </>
    )
}
